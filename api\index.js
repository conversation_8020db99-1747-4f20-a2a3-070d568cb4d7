import { config } from 'dotenv';
import express from 'express';
import session from 'express-session';
import connectPgSimple from 'connect-pg-simple';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import { spawn } from 'child_process';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configure dotenv to load from parent directory
const envPath = path.join(__dirname, '..', '.env');
config({ path: envPath });

// Initialize the app
const app = express();

// Middleware
app.use(cors({
  origin: process.env.NODE_ENV === 'production' ?
    [/\.vercel\.app$/, /localhost:\d+$/] :
    ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Global variables for SSE clients and status management
let sseClients = new Set();
let fsWatcher = null;

// Initialize server components
async function initializeServerComponents() {
  try {
    // Import modules that depend on environment variables
    const { setupAuth } = await import('../server/auth.js');
    const database = (await import('../server/database.js')).default;

    // Session configuration
    let sessionConfig = {
      secret: process.env.SESSION_SECRET || 'your-secret-key-change-in-production',
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      }
    };

    // Use PostgreSQL for session storage if database is available
    if (process.env.USE_DATABASE !== 'false' && process.env.DATABASE_URL) {
      try {
        const PgSession = connectPgSimple(session);
        sessionConfig.store = new PgSession({
          conString: process.env.DATABASE_URL,
          tableName: 'session',
          createTableIfMissing: true
        });
        console.log('🗄️  Using PostgreSQL for session storage');
      } catch (error) {
        console.log('📁 Using memory store for sessions (fallback)');
      }
    } else {
      console.log('📁 Using memory store for sessions');
    }

    app.use(session(sessionConfig));

    // Setup authentication routes
    setupAuth(app);

    return { database };
  } catch (error) {
    console.error('Failed to initialize server components:', error);
    throw error;
  }
}

// Initialize components
let serverComponents = null;
const initPromise = initializeServerComponents().then(components => {
  serverComponents = components;
  return components;
}).catch(error => {
  console.error('Server initialization failed:', error);
  throw error;
});

// Agent mode endpoint - requires authentication
app.post('/api/agent', async (req, res) => {
  try {
    // Ensure server is initialized
    await initPromise;

    // Check if user is authenticated
    if (!req.session.userId) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Please sign in to access Agent Mode'
      });
    }

    const { prompt } = req.body;

    if (!prompt || prompt.trim() === '') {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    console.log(`🤖 Agent mode request from user ${req.session.user.email}:`, prompt);

    // For serverless environment, we'll simulate agent execution
    // and provide immediate feedback while processing in background
    const agentResponse = await simulateAgentExecution(prompt, req.session.user);

    res.json(agentResponse);

  } catch (error) {
    console.error('💥 Agent mode error:', error);
    res.status(500).json({
      error: 'Failed to start agent mode',
      details: error.message
    });
  }
});

// Simulate agent execution for serverless environment
async function simulateAgentExecution(prompt, user) {
  try {
    // Update status to indicate processing
    const statusData = {
      status: 'processing',
      message: `Processing request: "${prompt}"`,
      projectName: null,
      projectPath: null,
      toolCall: null,
      toolResult: null,
      timestamp: new Date().toISOString(),
      sessionId: Buffer.from(prompt).toString('base64').substring(0, 10),
      user: user.email
    };

    // Write status file for SSE streaming
    await updateAgentStatus(statusData);

    // Simulate agent thinking and processing
    setTimeout(async () => {
      await updateAgentStatus({
        ...statusData,
        status: 'thinking',
        message: 'AI is analyzing your request and planning the implementation...'
      });
    }, 1000);

    setTimeout(async () => {
      await updateAgentStatus({
        ...statusData,
        status: 'executing',
        message: 'Generating code and creating project structure...'
      });
    }, 3000);

    setTimeout(async () => {
      await updateAgentStatus({
        ...statusData,
        status: 'completed',
        message: '✅ Project created successfully! In a local environment, this would open in VSCode.',
        projectName: 'ai-generated-project',
        projectPath: '/tmp/ai-generated-project'
      });
    }, 6000);

    return {
      success: true,
      message: 'Agent mode activated! Processing your request...',
      prompt: prompt,
      status: 'running',
      timestamp: new Date().toISOString(),
      note: 'In serverless environment: VSCode integration is simulated. Full agent capabilities available in local development.'
    };

  } catch (error) {
    console.error('Agent simulation error:', error);
    throw error;
  }
}

// Update agent status function
async function updateAgentStatus(statusData) {
  try {
    const agentDir = path.join(__dirname, '..', 'agent');
    const statusFilePath = path.join(agentDir, 'agent-status.json');

    // Ensure agent directory exists
    if (!fs.existsSync(agentDir)) {
      fs.mkdirSync(agentDir, { recursive: true });
    }

    // Write status file
    fs.writeFileSync(statusFilePath, JSON.stringify(statusData, null, 2));
    console.log(`📊 Status updated: ${statusData.status} - ${statusData.message}`);

    // Broadcast to SSE clients
    broadcastAgentStatus(statusData);
  } catch (error) {
    console.error('❌ Failed to update status:', error.message);
  }
}

// Function to broadcast status updates to all SSE clients
function broadcastAgentStatus(statusData) {
  const message = `data: ${JSON.stringify({ type: 'status', ...statusData })}\n\n`;

  // Remove disconnected clients
  const deadClients = [];

  sseClients.forEach(client => {
    try {
      client.res.write(message);
    } catch (error) {
      console.error(`📡 Error sending to SSE client ${client.id}:`, error.message);
      deadClients.push(client);
    }
  });

  // Clean up dead connections
  deadClients.forEach(client => sseClients.delete(client));

  if (sseClients.size > 0) {
    console.log(`📡 Broadcasted status to ${sseClients.size} SSE clients`);
  }
}

// API health check
app.get('/api/health', async (req, res) => {
  try {
    await initPromise;
    const dbHealth = await serverComponents.database.healthCheck();
    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      database: dbHealth,
      environment: process.env.NODE_ENV || 'development',
      platform: 'vercel-serverless'
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// Agent status endpoint - requires authentication
app.get('/api/agent/status', async (req, res) => {
  try {
    await initPromise;

    // Check if user is authenticated
    if (!req.session.userId) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Please sign in to access Agent Mode status'
      });
    }

    const agentPath = path.join(__dirname, '..', 'agent');
    const statusFilePath = path.join(agentPath, 'agent-status.json');

    // Check if status file exists
    if (fs.existsSync(statusFilePath)) {
      const statusData = JSON.parse(fs.readFileSync(statusFilePath, 'utf8'));
      res.json(statusData);
    } else {
      res.json({
        status: 'unknown',
        message: 'No status information available',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: `Failed to read status: ${error.message}`,
      timestamp: new Date().toISOString()
    });
  }
});

// SSE endpoint for real-time agent status streaming
app.get('/api/agent/status/stream', async (req, res) => {
  try {
    await initPromise;

    // Check if user is authenticated
    if (!req.session.userId) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Please sign in to access Agent Mode status stream'
      });
    }

    // Set up SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Send initial connection message
    res.write('data: {"type": "connected", "message": "SSE connection established"}\n\n');

    // Add client to the set
    const clientId = Date.now() + Math.random();
    const client = { id: clientId, res, userId: req.session.userId };
    sseClients.add(client);

    console.log(`📡 SSE client connected: ${clientId} (${sseClients.size} total clients)`);

    // Send current status immediately if available
    try {
      const agentPath = path.join(__dirname, '..', 'agent');
      const statusFilePath = path.join(agentPath, 'agent-status.json');

      if (fs.existsSync(statusFilePath)) {
        const statusData = JSON.parse(fs.readFileSync(statusFilePath, 'utf8'));
        res.write(`data: ${JSON.stringify({ type: 'status', ...statusData })}\n\n`);
      }
    } catch (error) {
      console.error('Error sending initial status:', error);
    }

    // Handle client disconnect
    req.on('close', () => {
      sseClients.delete(client);
      console.log(`📡 SSE client disconnected: ${clientId} (${sseClients.size} total clients)`);
    });

    req.on('error', (error) => {
      console.error(`📡 SSE client error: ${clientId}`, error);
      sseClients.delete(client);
    });

  } catch (error) {
    console.error('SSE endpoint error:', error);
    res.status(500).json({ error: 'Failed to establish SSE connection' });
  }
});

// Serve static files for non-API routes
app.get('*', (req, res) => {
  const filePath = req.path;

  // Handle root route
  if (filePath === '/') {
    return res.sendFile(path.join(__dirname, '..', 'public', 'index.html'));
  }

  // Handle static files
  const staticFilePath = path.join(__dirname, '..', 'public', filePath);

  // Check if file exists
  if (fs.existsSync(staticFilePath)) {
    return res.sendFile(staticFilePath);
  }

  // For non-existent routes, serve the main app (SPA behavior)
  res.sendFile(path.join(__dirname, '..', 'public', 'index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    error: process.env.NODE_ENV === 'production' ? 'Internal Server Error' : err.message
  });
});

export default app;
