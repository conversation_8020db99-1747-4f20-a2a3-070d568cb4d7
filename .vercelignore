# Development files
node_modules
.env.local
.env.development
.env.test

# Build artifacts
.next
dist
build

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file (keep .env for production)
# .env

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test files
test/
tests/
__tests__/
*.test.js
*.spec.js

# Documentation (optional - remove if you want docs deployed)
docs/
*.md
!README.md

# Backup files
*.backup.*
server/users.backup.*

# Local development scripts
scripts/migrate-to-postgres.js

# Debug files
debug*.html
*-test.html
*-diagnostic.html
